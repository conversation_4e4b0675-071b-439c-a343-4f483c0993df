#!/usr/bin/env python3
"""
Plant Disease Detection System - Startup Script
This script helps you start the application easily.
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_python():
    """Check if Python is available"""
    try:
        subprocess.run([sys.executable, "--version"], check=True, capture_output=True)
        return True
    except:
        print("❌ Python not found!")
        return False

def check_dependencies():
    """Check if required packages are installed"""
    try:
        import flask
        import tensorflow
        import PIL
        import numpy
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("💡 Run: pip install -r backend/requirements.txt")
        return False

def check_model():
    """Check if the weed detection model file exists"""
    model_path = Path("weed_detection_model.keras")
    if model_path.exists():
        print("✅ Weed detection model file found")
        return True
    else:
        print("❌ Model file 'weed_detection_model.keras' not found!")
        print("💡 Please copy your trained weed model from the notebook to this directory")
        return False

def start_backend():
    """Start the Flask backend server"""
    print("🚀 Starting backend server...")
    
    # Change to backend directory
    os.chdir("backend")
    
    try:
        # Start the Flask app
        subprocess.run([sys.executable, "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Backend server stopped")
    except Exception as e:
        print(f"❌ Error starting backend: {e}")

def start_frontend():
    """Start a simple HTTP server for the frontend"""
    print("🌐 Starting frontend server...")
    
    try:
        # Start HTTP server
        subprocess.run([sys.executable, "-m", "http.server", "8000"], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Frontend server stopped")
    except Exception as e:
        print(f"❌ Error starting frontend: {e}")

def main():
    """Main startup function"""
    print("🌿 Automated Weed Detection System")
    print("=" * 40)
    
    # Check requirements
    if not check_python():
        return
    
    if not check_dependencies():
        return
    
    if not check_model():
        return
    
    print("\n📋 Setup Instructions:")
    print("1. Backend will start on http://localhost:5000")
    print("2. Frontend will be available at http://localhost:8000")
    print("3. Open your browser and go to http://localhost:8000")
    print("\n💡 Press Ctrl+C to stop the servers")
    
    # Ask user what to start
    print("\n🎯 What would you like to start?")
    print("1. Backend only (for API testing)")
    print("2. Frontend only (if backend is already running)")
    print("3. Both (recommended)")
    
    choice = input("\nEnter your choice (1/2/3): ").strip()
    
    if choice == "1":
        start_backend()
    elif choice == "2":
        start_frontend()
    elif choice == "3":
        print("🔄 Starting both servers...")
        print("💡 Backend will start first, then frontend")
        time.sleep(2)
        start_backend()
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main() 